#!/usr/bin/env python3
"""
Group Visualization Script for Image Predictions

This script creates an HTML visualization of image predictions grouped by group number.
Each group is displayed as a row with images showing their true and predicted labels.
Images are framed in green (correct) or red (incorrect) based on prediction accuracy.

Usage:
    python group_visualize.py <csv_file_path> <output_html_path>

Example:
    python group_visualize.py output/2025-05-16_14-36-44/test_preds_new.csv visualization.html
"""

import pandas as pd
import argparse
import sys
from pathlib import Path


def create_html_visualization(csv_file_path, output_html_path):
    """
    Create HTML visualization of grouped image predictions.
    
    Args:
        csv_file_path (str): Path to the CSV file containing predictions
        output_html_path (str): Path where the HTML file will be saved
    """
    
    # Read the CSV file
    try:
        df = pd.read_csv(csv_file_path)
        print(f"Loaded {len(df)} records from {csv_file_path}")
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        sys.exit(1)
    
    # Validate required columns
    required_columns = ['url', 'group', 'label', 'ensemble']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"Error: Missing required columns: {missing_columns}")
        sys.exit(1)
    
    # Group by group number
    grouped = df.groupby('group')
    
    # Start building HTML
    html_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Prediction Visualization</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        
        .group-container {
            margin-bottom: 40px;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .group-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #ddd;
            padding-bottom: 5px;
        }
        
        .images-row {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: flex-start;
        }
        
        .image-container {
            border: 4px solid;
            border-radius: 8px;
            padding: 10px;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 200px;
        }
        
        .image-container.correct {
            border-color: #28a745;
        }
        
        .image-container.incorrect {
            border-color: #dc3545;
        }
        
        .image-container img {
            width: 100%;
            height: auto;
            max-width: 180px;
            border-radius: 4px;
        }
        
        .labels {
            margin-top: 8px;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .label-text {
            margin: 2px 0;
        }
        
        .label-text strong {
            font-weight: bold;
        }
        
        .stats {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .stats-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Image Prediction Visualization</h1>
        <p>Images grouped by group number with prediction accuracy visualization</p>
    </div>
"""
    
    # Calculate overall statistics
    total_images = len(df)
    correct_predictions = len(df[df['label'] == df['ensemble']])
    accuracy = (correct_predictions / total_images) * 100 if total_images > 0 else 0
    
    # Add statistics section
    html_content += f"""
    <div class="stats">
        <div class="stats-title">Overall Statistics</div>
        <div>Total Images: {total_images}</div>
        <div>Correct Predictions: {correct_predictions}</div>
        <div>Incorrect Predictions: {total_images - correct_predictions}</div>
        <div>Accuracy: {accuracy:.2f}%</div>
        <div>Total Groups: {len(grouped)}</div>
    </div>
"""
    
    # Process each group
    for group_id, group_data in grouped:
        group_correct = len(group_data[group_data['label'] == group_data['ensemble']])
        group_total = len(group_data)
        group_accuracy = (group_correct / group_total) * 100 if group_total > 0 else 0
        
        html_content += f"""
    <div class="group-container">
        <div class="group-title">
            Group {group_id} ({group_total} images, {group_accuracy:.1f}% accuracy)
        </div>
        <div class="images-row">
"""
        
        # Process each image in the group
        for _, row in group_data.iterrows():
            url = row['url']
            true_label = row['label']
            pred_label = row['ensemble']
            
            # Determine if prediction is correct
            is_correct = true_label == pred_label
            frame_class = "correct" if is_correct else "incorrect"
            
            html_content += f"""
            <div class="image-container {frame_class}">
                <img src="{url}" alt="Image from group {group_id}" loading="lazy">
                <div class="labels">
                    <div class="label-text"><strong>true:</strong> {true_label}</div>
                    <div class="label-text"><strong>pred:</strong> {pred_label}</div>
                </div>
            </div>
"""
        
        html_content += """
        </div>
    </div>
"""
    
    # Close HTML
    html_content += """
</body>
</html>
"""
    
    # Write HTML file
    try:
        with open(output_html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"HTML visualization saved to: {output_html_path}")
        print(f"Open the file in a web browser to view the visualization.")
    except Exception as e:
        print(f"Error writing HTML file: {e}")
        sys.exit(1)


def main():
    """Main function to handle command line arguments and execute visualization."""
    
    parser = argparse.ArgumentParser(
        description="Create HTML visualization of grouped image predictions",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python group_visualize.py data.csv output.html
    python group_visualize.py output/2025-05-16_14-36-44/test_preds_new.csv visualization.html
        """
    )
    
    parser.add_argument(
        'csv_file',
        help='Path to the CSV file containing image predictions'
    )
    
    parser.add_argument(
        'output_html',
        help='Path where the HTML visualization will be saved'
    )
    
    args = parser.parse_args()
    
    # Validate input file exists
    if not Path(args.csv_file).exists():
        print(f"Error: CSV file '{args.csv_file}' does not exist.")
        sys.exit(1)
    
    # Create output directory if it doesn't exist
    output_path = Path(args.output_html)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Create visualization
    create_html_visualization(args.csv_file, args.output_html)


if __name__ == "__main__":
    main()
