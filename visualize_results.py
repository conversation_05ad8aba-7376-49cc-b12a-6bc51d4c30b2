#!/usr/bin/env python3
"""
Visualization script for flat detection results.

This script takes a CSV file with image URLs, true labels, and predicted labels,
and generates an HTML file that displays the images with color-coded frames
(green for correct predictions, red for incorrect) and provides filtering options.
"""

import argparse
import csv
import os
from typing import List, Dict, Set, Optional


def read_csv_data(csv_path: str) -> List[Dict]:
    """
    Read the CSV file and return a list of dictionaries with image data.
    
    Args:
        csv_path: Path to the CSV file
        
    Returns:
        List of dictionaries with keys: url, label, ensemble
    """
    data = []
    with open(csv_path, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            data.append({
                'url': row['url'],
                'label': row['label'],
                'ensemble': row['ensemble']
            })
    return data


def get_unique_labels(data: List[Dict]) -> Dict[str, Set[str]]:
    """
    Extract unique true and predicted labels from the data.
    
    Args:
        data: List of dictionaries with image data
        
    Returns:
        Dictionary with keys 'true' and 'pred' containing sets of unique labels
    """
    true_labels = set()
    pred_labels = set()
    
    for item in data:
        true_labels.add(item['label'])
        pred_labels.add(item['ensemble'])
    
    return {
        'true': true_labels,
        'pred': pred_labels
    }


def generate_html(data: List[Dict], unique_labels: Dict[str, Set[str]], output_path: str) -> None:
    """
    Generate HTML file with image visualization and filtering options.
    
    Args:
        data: List of dictionaries with image data
        unique_labels: Dictionary with unique true and predicted labels
        output_path: Path to save the HTML file
    """
    html_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flat Detection Results</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .filters {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .filter-group {
            margin-bottom: 10px;
        }
        label {
            margin-right: 10px;
            font-weight: bold;
        }
        select {
            padding: 5px;
            border-radius: 3px;
            border: 1px solid #ccc;
        }
        .image-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .image-item {
            width: 200px;
            margin-bottom: 20px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .image-wrapper {
            padding: 10px;
        }
        .correct {
            border: 4px solid #4CAF50;
        }
        .incorrect {
            border: 4px solid #F44336;
        }
        img {
            width: 100%;
            height: 200px;
            object-fit: contain;
        }
        .labels {
            padding: 10px;
            text-align: center;
            font-size: 14px;
        }
        .hidden {
            display: none;
        }
        button {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <h1>Flat Detection Results</h1>
    
    <div class="filters">
        <div class="filter-group">
            <label for="true-label">True Label:</label>
            <select id="true-label">
                <option value="none">None (No filter)</option>
"""
    
    # Add true label options
    for label in sorted(unique_labels['true']):
        html_content += f'                <option value="{label}">{label}</option>\n'
    
    html_content += """
            </select>
        </div>
        
        <div class="filter-group">
            <label for="pred-label">Predicted Label:</label>
            <select id="pred-label">
                <option value="none">None (No filter)</option>
"""
    
    # Add predicted label options
    for label in sorted(unique_labels['pred']):
        html_content += f'                <option value="{label}">{label}</option>\n'
    
    html_content += """
            </select>
        </div>
        
        <button id="apply-filters">Apply Filters</button>
    </div>
    
    <div class="image-container">
"""
    
    # Add images
    for item in data:
        is_correct = item['label'] == item['ensemble']
        frame_class = "correct" if is_correct else "incorrect"
        
        html_content += f"""
        <div class="image-item" data-true="{item['label']}" data-pred="{item['ensemble']}">
            <div class="image-wrapper {frame_class}">
                <img src="{item['url']}" alt="Image" loading="lazy">
            </div>
            <div class="labels">
                <div>True: <strong>{item['label']}</strong></div>
                <div>Pred: <strong>{item['ensemble']}</strong></div>
            </div>
        </div>
"""
    
    html_content += """
    </div>

    <script>
        document.getElementById('apply-filters').addEventListener('click', function() {
            const trueLabel = document.getElementById('true-label').value;
            const predLabel = document.getElementById('pred-label').value;
            
            const imageItems = document.querySelectorAll('.image-item');
            
            imageItems.forEach(item => {
                const itemTrueLabel = item.getAttribute('data-true');
                const itemPredLabel = item.getAttribute('data-pred');
                
                let showItem = true;
                
                if (trueLabel !== 'none' && itemTrueLabel !== trueLabel) {
                    showItem = false;
                }
                
                if (predLabel !== 'none' && itemPredLabel !== predLabel) {
                    showItem = false;
                }
                
                if (showItem) {
                    item.classList.remove('hidden');
                } else {
                    item.classList.add('hidden');
                }
            });
        });
    </script>
</body>
</html>
"""
    
    with open(output_path, 'w') as f:
        f.write(html_content)


def main():
    """Main function to process arguments and generate the visualization."""
    parser = argparse.ArgumentParser(description='Generate HTML visualization for flat detection results')
    parser.add_argument('csv_path', help='Path to the CSV file with prediction results')
    parser.add_argument('output_path', help='Path to save the output HTML file')
    
    args = parser.parse_args()
    
    # Check if input file exists
    if not os.path.exists(args.csv_path):
        print(f"Error: Input file '{args.csv_path}' does not exist")
        return
    
    # Read data from CSV
    data = read_csv_data(args.csv_path)
    
    # Get unique labels
    unique_labels = get_unique_labels(data)
    
    # Generate HTML
    generate_html(data, unique_labels, args.output_path)
    
    print(f"Visualization generated successfully at: {args.output_path}")
    print(f"Total images: {len(data)}")
    print(f"Unique true labels: {', '.join(sorted(unique_labels['true']))}")
    print(f"Unique predicted labels: {', '.join(sorted(unique_labels['pred']))}")


if __name__ == "__main__":
    main()
