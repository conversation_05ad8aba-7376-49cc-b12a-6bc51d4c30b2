import torch
import torch.nn as nn
from torchvision.models import vgg16
from torchvision.models.vgg import VGG16_Weights



class VGGClassifier(nn.Module):
    def __init__(
        self,
        n_classes: int = 3,
    ):
        super().__init__()
        self.encoder = vgg16(weights=VGG16_Weights.IMAGENET1K_V1)
        for param in self.encoder.parameters():
            param.requires_grad = False
        
        for name, param in self.encoder.named_parameters():
            if name.startswith('classifier.6'):
                param.requires_grad = True
        self.last_layer = nn.Linear(1000, n_classes)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.encoder(x)
        x = self.last_layer(x)
        return x
