import torch
import torch.nn as nn
from transformers import AutoModel


class DinoWithTransformerClassifier(nn.Module):
    def __init__(
        self,
        n_transformer_layers: int = 1,
        n_classes: int = 3,
        **kwargs
    ):
        super().__init__()
        self.encoder = AutoModel.from_pretrained("facebook/dinov2-base")
        for param in self.encoder.parameters():
            param.requires_grad = False

        self.transformer = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(d_model=768, nhead=8),
            num_layers=n_transformer_layers
        )
        self.last_layer = nn.Linear(768, n_classes)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.encoder(x).last_hidden_state
        x = self.transformer(x)
        x = self.last_layer(x[:, 0, :])
        return x
    