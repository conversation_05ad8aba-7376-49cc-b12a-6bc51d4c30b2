import torch
import torch.nn as nn
from transformers import AutoModel


class DinoClassifier(nn.Module):
    def __init__(
        self,
        n_layers: int = 2,
        n_classes: int = 3,
        d_hidden: int = 256,
        **kwargs
    ):
        super().__init__()
        self.encoder = AutoModel.from_pretrained("facebook/dinov2-base")
        for param in self.encoder.parameters():
            param.requires_grad = False

        self.projector = nn.Linear(768, d_hidden)
        
        self.hidden_layers = nn.Sequential(
            *[
                nn.Sequential(
                    nn.Linear(d_hidden, d_hidden),
                    nn.ReLU()
                )
                for _ in range(n_layers)
            ]
        )
        self.last_layer = nn.Linear(d_hidden, n_classes)
        self.log_softmax = nn.LogSoftmax(dim=1)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        if len(x.shape) == 4:
            x = self.encoder(x).pooler_output
        x = self.projector(x)
        x = self.hidden_layers(x)
        x = self.last_layer(x)
        return x
    