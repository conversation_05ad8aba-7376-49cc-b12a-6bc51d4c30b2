from .dino_model import DinoClassifier
from .dino_with_transformer_model import DinoWithTransformerClassifier
from .vgg_model import VGGClassifier



class ModelMapper:
    model_map = {
        "DinoClassifier": DinoClassifier,
        "DinoWithTransformerClassifier": DinoWithTransformerClassifier,
        "VGGClassifier": VGGClassifier
    }
        
    @classmethod
    def get(cls, name, params):
        return cls.model_map[name](**params)
    