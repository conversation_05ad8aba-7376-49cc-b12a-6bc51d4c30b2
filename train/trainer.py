import datetime
import io
import json
import os
import random

from matplotlib import pyplot as plt
import numpy as np
import pandas as pd
from PIL import Image
from sklearn.metrics import accuracy_score, confusion_matrix, ConfusionMatrixDisplay
import torch
from torch.optim import Adam
from torch.utils.data import DataLoader
from torch.utils import tensorboard
from torchvision.transforms import ToTensor
from tqdm.auto import tqdm

from .dataset import CustomDataset, CustomBatchSampler
from .wrappers import WrapperMapper

# Set all random seeds to 42
random.seed(42)
np.random.seed(42)
torch.manual_seed(42)
torch.cuda.manual_seed_all(42)
torch.backends.cudnn.deterministic = True
torch.backends.cudnn.benchmark = False


class Trainer:
    def __init__(self, cfg: dict):
        n_cross_validation = cfg['n_cross_validation']
        train_data = cfg['train_data']
        test_data = cfg['test_data']
        
        train_data = pd.read_csv(train_data)
        test_data = pd.read_csv(test_data)
        
        groups = train_data.group.unique().tolist()
        
        sub_groups = []
        for i in range(n_cross_validation):
            sub_groups.append(random.sample(groups, int(len(groups)*0.8)))
            
        self.train_data = train_data
        self.test_data = test_data
        self.cfg = cfg
        self.sub_groups = sub_groups
        
        cur_date = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        self.result_path = os.path.join(cfg['result_dir'], cur_date)
        os.makedirs(self.result_path, exist_ok=True)
        with open(os.path.join(self.result_path, 'config.json'), 'w') as f:
            json.dump(cfg, f, indent=4)
    
    
    def __calculate_metrics(self, wrapper, loader):
        wrapper.eval()
        predictions_list = []
        targets_list = []
        for batch in tqdm(loader, desc="Calculating metrics"):
            images, targets = batch
            predictions = wrapper.inference_step(images).cpu().tolist()
            predictions_list.extend(predictions)
            targets_list.extend(targets)
        
        acc = accuracy_score(targets_list, predictions_list)
        cm = confusion_matrix(targets_list, predictions_list)   
        labels = sorted(list(set(targets_list)))
        cm_display = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=labels)
        fig, ax = plt.subplots(figsize=(6, 6))
        cm_display.plot(ax=ax, cmap=plt.cm.Blues, colorbar=True)
        plt.title('Confusion Matrix')
        plt.xlabel('Predicted label')
        plt.ylabel('True label')
        plt.tight_layout()
        
        buf = io.BytesIO()
        fig.savefig(buf, format='png')
        plt.close(fig)             
        buf.seek(0)
        pil_img = Image.open(buf)
        return acc, ToTensor()(pil_img)

    
    def __single_train(self, sub_group: list, result_path: str):
        # Load data
        train_data = self.train_data[self.train_data.group.isin(sub_group)]
        val_data = self.train_data[~self.train_data.group.isin(sub_group)]
        test_data = self.test_data
        
        train_dataset = CustomDataset(train_data)
        train_sampler = CustomBatchSampler(train_dataset, self.cfg['batch_size'])
        train_loader = DataLoader(train_dataset, batch_sampler=train_sampler, collate_fn=train_dataset.collate_fn, num_workers=4)
        val_dataset = CustomDataset(val_data)
        val_loader = DataLoader(val_dataset, batch_size=1, shuffle=False, collate_fn=val_dataset.collate_fn, num_workers=4)        
        test_dataset = CustomDataset(test_data)
        test_loader = DataLoader(test_dataset, batch_size=1, shuffle=False, collate_fn=test_dataset.collate_fn, num_workers=4)
        
        # Load wrapper
        wrapper_params = self.cfg['wrapper']['params']
        # wrapper_params.update({
        #     'criterion_weights': train_dataset.label_weights
        # })
        wrapper = WrapperMapper.get(self.cfg['wrapper']['name'], wrapper_params)
        
        # Initialize optimizer
        optimizer = Adam(wrapper.model.parameters(), lr=self.cfg['lr'])
        
        # Load tensorboard
        writer = tensorboard.SummaryWriter(log_dir=result_path)
        
        for epoch in range(self.cfg['n_epochs']):
            print(f"Epoch {epoch+1}/{self.cfg['n_epochs']}")
            wrapper.train()
            losses = []
            for batch in tqdm(train_loader, desc="Training"):
                optimizer.zero_grad()
                images, targets = batch
                loss = wrapper.train_step(images, targets)
                loss.backward()
                optimizer.step()
                losses.append(loss.item())
            
            avg_loss = np.mean(losses)
            print(f"Train loss: {avg_loss}")
            writer.add_scalar('train/loss', avg_loss, epoch)
            
            acc, cm_img = self.__calculate_metrics(wrapper, train_loader)
            writer.add_scalar('train/accuracy', acc, epoch)
            writer.add_image('train/confusion_matrix', cm_img, epoch)
            
            # Validation
            wrapper.eval()
            losses = []
            with torch.no_grad():
                for batch in tqdm(val_loader, desc="Validating"):
                    images, targets = batch
                    loss = wrapper.train_step(images, targets)
                    losses.append(loss.item())
            avg_loss = np.mean(losses)
            print(f"Validation loss: {avg_loss}")
            writer.add_scalar('val/loss', avg_loss, epoch)
            
            acc, cm_img = self.__calculate_metrics(wrapper, val_loader)
            writer.add_scalar('val/accuracy', acc, epoch)
            writer.add_image('val/confusion_matrix', cm_img, epoch)
            
            # Save model
            wrapper.save_model(f"{result_path}/model.pth")

        # Test metrics
        wrapper.eval()
        acc, cm_img = self.__calculate_metrics(wrapper, test_loader)
        writer.add_scalar('test/accuracy', acc, self.cfg['n_epochs'])
        writer.add_image('test/confusion_matrix', cm_img, self.cfg['n_epochs'])        
        writer.close()

    def train(self):
        for i, sub_group in enumerate(self.sub_groups):
            print(f"Cross-validation {i+1}/{self.cfg['n_cross_validation']}")
            result_path = os.path.join(self.result_path, f"cv_{i+1}")
            os.makedirs(result_path, exist_ok=True)
            self.__single_train(sub_group, result_path)
