from .dino_wrapper import DinoWrapper
from .dino_with_transformer_wrapper import DinoWithTransformerWrapper
from .vgg_wrapper import VGGWrapper
from .base_wrapper import BaseWrapper


class WrapperMapper:
    model_map = {
        "DinoWrapper": <PERSON><PERSON>rapper,
        "DinoWithTransformerWrapper": DinoWithTransformerWrapper,
        "VGGWrapper": VGGWrapper
    }
        
    @classmethod
    def get(cls, name, params) -> BaseWrapper:
        return cls.model_map[name](**params)
    