from typing import List, Optional
import torch
from torchvision.models.vgg import VGG16_Weights
from PIL import Image

from .base_wrapper import BaseWrapper
from train.models import ModelMapper



class VGGWrapper(BaseWrapper):
    def __init__(
        self,
        model_cfg: dict,
        device: str,
        criterion_weights: Optional[List[float]] = None
    ):
        super().__init__(
            model_cfg = model_cfg,
            device = device,
            criterion_weights = criterion_weights
        )
        self.processor = VGG16_Weights.IMAGENET1K_V1.transforms(resize_size=224)
        
    def load_model(self, model_cfg):
        if model_cfg['name'] != "VGGClassifier":
            raise ValueError(f"Wrong model name: {model_cfg['name']}, expected VGGClassifier")
        model = ModelMapper.get(model_cfg['name'], model_cfg['params'])
        print(f"Number of params: {sum(p.numel() for p in model.parameters())}")
        print(f"Number of params, requires grad: {sum(p.numel() for p in model.parameters() if p.requires_grad)}")
        return model

    def process(self, images, augment: bool = False):
        if isinstance(images, Image.Image):
            images = [images]
        images = [self.add_padding(image) for image in images]
        if augment:
            images = [self.augment(image) for image in images]
        return torch.stack([self.processor(image) for image in images])
    
    