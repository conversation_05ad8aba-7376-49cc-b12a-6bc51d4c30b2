from email.mime import image
from typing import List, Optional
import torch
from transformers import AutoImageProcessor
from PIL import Image

from .base_wrapper import BaseWrapper
from train.models import ModelMapper



class DinoWithTransformerWrapper(BaseWrapper):
    def __init__(
        self,
        model_cfg: dict,
        device: str,
        criterion_weights: Optional[List[float]] = None
    ):
        super().__init__(
            model_cfg = model_cfg,
            device = device,
            criterion_weights = criterion_weights
        )
        self.processor = AutoImageProcessor.from_pretrained("facebook/dinov2-base", do_center_crop=False)
        
    
    def load_model(self, model_cfg):
        if model_cfg['name'] != "DinoWithTransformerClassifier":
            raise ValueError(f"Wrong model name: {model_cfg['name']}, expected DinoClassifier")
        model = ModelMapper.get(model_cfg['name'], model_cfg['params'])
        print(f"Number of params: {sum(p.numel() for p in model.parameters())}")
        print(f"Number of params, requires grad: {sum(p.numel() for p in model.parameters() if p.requires_grad)}")
        return model
    
    # def add_padding(self, img : Image.Image) -> Image.Image:
    #     w, h = img.size
    #     max_size = max(w,h)
    #     left = abs(max_size-w)//2
    #     top = abs(max_size-h)//2
    #     result = Image.new(img.mode, (max_size, max_size), 0)
    #     result.paste(img, (left, top))
    #     return result
    
    def process(self, images, augment: bool = False):
        if isinstance(images, Image.Image):
            images = [images]
        images = [self.add_padding(image) for image in images]
        if augment:
            images = [self.augment(image) for image in images]
        return self.processor(images=images, return_tensors="pt")['pixel_values']
    
    # def train_step(self, model_input, target):
    #     if isinstance(target, int):
    #         target = [target]
    #     target = torch.tensor(target).to(self.device)
    #     model_input = self.process(model_input, augment=True)
    #     model_input = model_input.to(self.device)
    #     model_output = self.model(model_input)
    #     loss = self.loss_fn(model_output, target)
    #     return loss

    # @torch.no_grad()
    # def inference_step(self, model_input):
    #     model_input = self.process(model_input)
    #     model_input = model_input.to(self.device)
    #     model_output = self.model(model_input)
    #     return torch.max(model_output, dim=1)[1]
    