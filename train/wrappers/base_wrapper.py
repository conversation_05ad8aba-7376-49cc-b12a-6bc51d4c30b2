from typing import Optional, Union, List
from abc import ABC, abstractmethod

import albumentations as A
from PIL import Image
import numpy as np
import torch
import torch.nn as nn


class BaseWrapper(ABC):
    def __init__(
        self,
        model_cfg: dict,
        device: str,
        criterion_weights: Optional[List[float]] = None
    ):
        self.model = self.load_model(model_cfg)
        if "pretrained_weights" in model_cfg:
            self.model.load_state_dict(torch.load(model_cfg['pretrained_weights'], weights_only=True))
            print(f"Loaded pretrained weights from {model_cfg['pretrained_weights']}")
        self.model.to(device)
        
        self.device = device
        
        self.augment_transformation = A.Compose([
            A.HorizontalFlip(p=0.5)
        ])
        self.loss_fn = nn.CrossEntropyLoss(weight=torch.tensor(criterion_weights, device=device) if criterion_weights is not None else None)
    
    
    def train(self):
        self.model.train()
        
    def eval(self):
        self.model.eval()
    
    def save_model(self, path: str) -> nn.Module:
        # self.model.to('cpu')
        torch.save(self.model.state_dict(), path)
    
    def augment(self, image: Image.Image) -> Image.Image:
        image = np.array(image)
        return Image.fromarray(self.augment_transformation(image=image)['image'])
    
    def add_padding(self, img : Image.Image) -> Image.Image:
        w, h = img.size
        max_size = max(w,h)
        left = abs(max_size-w)//2
        top = abs(max_size-h)//2
        result = Image.new(img.mode, (max_size, max_size), 0)
        result.paste(img, (left, top))
        return result
    
    @abstractmethod
    def load_model(self, model_cfg: dict) -> nn.Module:
        pass
    
    @abstractmethod
    def process(self, images: Union[Image.Image, List[Image.Image]], augment: bool = False) -> torch.Tensor:
        pass
    
    # @abstractmethod
    def train_step(self, model_input: Union[Image.Image, List[Image.Image]], target: Union[List[int], int]) -> torch.Tensor:
        if isinstance(target, int):
            target = [target]
        target = torch.tensor(target).to(self.device)
        model_input = self.process(model_input, augment=True)
        model_input = model_input.to(self.device)
        model_output = self.model(model_input)
        loss = self.loss_fn(model_output, target)
        return loss
    
    # @abstractmethod
    @torch.no_grad()
    def inference_step(self, model_input: Union[Image.Image, List[Image.Image], torch.Tensor]) -> torch.Tensor:
        if not isinstance (model_input, torch.Tensor):
            model_input = self.process(model_input)
            model_input = model_input.to(self.device)
        model_output = self.model(model_input)
        return torch.max(model_output, dim=1)[1]
    