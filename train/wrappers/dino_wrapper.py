from email.mime import image
from typing import List, Optional
import torch
from transformers import AutoImageProcessor
from PIL import Image

from .base_wrapper import BaseWrapper
from train.models import ModelMapper



class DinoWrapper(BaseWrapper):
    def __init__(
        self,
        model_cfg: dict,
        device: str,
        criterion_weights: Optional[List[float]] = None
    ):
        super().__init__(
            model_cfg = model_cfg,
            device = device,
            criterion_weights = criterion_weights
        )
        self.processor = AutoImageProcessor.from_pretrained("facebook/dinov2-base", do_center_crop=False)    
    
    def load_model(self, model_cfg):
        if model_cfg['name'] != "DinoClassifier":
            raise ValueError(f"Wrong model name: {model_cfg['name']}, expected DinoClassifier")
        model = ModelMapper.get(model_cfg['name'], model_cfg['params'])
        print(f"Number of params: {sum(p.numel() for p in model.parameters())}")
        print(f"Number of params, requires grad: {sum(p.numel() for p in model.parameters() if p.requires_grad)}")
        return model
    
    def process(self, images, augment: bool = False):
        if isinstance(images, Image.Image):
            images = [images]
        images = [self.add_padding(image) for image in images]
        if augment:
            images = [self.augment(image) for image in images]
        return self.processor(images=images, return_tensors="pt")['pixel_values']
    
    def process_with_dino(self, images):
        if isinstance(images, Image.Image):
            images = [images]
        images = [self.add_padding(image) for image in images]
        inputs = self.processor(images=images, return_tensors="pt")['pixel_values'].to(self.device)
        return self.model.encoder(inputs).pooler_output
    