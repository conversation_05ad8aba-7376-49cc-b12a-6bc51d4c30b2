import random
from typing import Union
from collections import defaultdict

import pandas as pd
from PIL import Image
from torch.utils.data import Dataset, BatchSampler


class CustomDataset(Dataset):
    def __init__(
        self,
        data: Union[pd.DataFrame, str]
    ):
        if isinstance(data, str):
            data = pd.read_csv(data)
        self.data = data
        label_counts = self.data['label_numeric'].value_counts().to_dict()
        total = 0
        for label, count in label_counts.items():
            total += (1/count)**0.5
        self.label_weights = [0]*len(label_counts)
        for label, count in label_counts.items():
            self.label_weights[label] = (1/count)**0.5 / total
        
    def __resize(self, image: Image.Image) -> Image.Image:
        w, h = image.size
        new_w = 256    
        new_h = int((new_w / w) * h)
        return image.resize((new_w, new_h), Image.BICUBIC)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx: int) -> tuple:
        sample = self.data.iloc[idx]
        image = Image.open(sample['image_path'])
        image = self.__resize(image)
        label = sample['label_numeric']
        return image, label
    
    def collate_fn(self, batch):
        images, labels = zip(*batch)
        return list(images), [label.item() for label in labels] 


class CustomBatchSampler(BatchSampler):
    def __init__(self, dataset, batch_size):
        self.indices_per_label = {}
        for i in range(len(dataset)):
            label = dataset.data.iloc[i]['label_numeric'].item()
            if label not in self.indices_per_label:
                self.indices_per_label[label] = []
            self.indices_per_label[label].append(i)
        self.batch_size = batch_size
        self.dataset_size = len(dataset)
        self.labels = list(self.indices_per_label.keys())
        
    def __len__(self):
        return self.dataset_size // self.batch_size
    
    def __iter__(self):
        for i in range(len(self)):
            labels = random.choices(self.labels, k=self.batch_size)
            indices = []
            for label in set(labels):
                occurances = labels.count(label)
                indices.extend(random.sample(self.indices_per_label[label], occurances))
            yield indices
